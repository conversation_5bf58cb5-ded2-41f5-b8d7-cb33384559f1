<template>
  <div class="center-panel">
    <!-- 中国3D地图 -->
    <div class="map-container">
      <div ref="mapChartRef" class="map-chart"></div>
    </div>

    <!-- 关键指标概览 -->
    <div class="metrics-container">
      <div class="metrics-grid">
        <div
          v-for="(metric, index) in metricsData"
          :key="index"
          class="metric-card"
          :style="{ background: metric.background }"
        >
          <div class="metric-content">
            <div class="metric-header">
              <span class="metric-title">{{ metric.title }}</span>
              <Icon :icon="metric.icon" :size="24" class="metric-icon" />
            </div>
            <div class="metric-value">{{ metric.value }}</div>
            <div class="metric-change" :style="{ color: metric.changeColor }">
              <Icon :icon="metric.changeIcon" :size="14" />
              <span>{{ metric.change }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
  import { ref, onMounted } from 'vue';
  import Icon from '@/components/Icon/Icon.vue';
  import { useECharts } from '@/hooks/web/useECharts';
  import echarts from '@/utils/lib/echarts';

  const mapChartRef = ref();
  const { setOptions: setMapOptions, getInstance } = useECharts(mapChartRef);

  // 关键指标数据
  const metricsData = ref([
    {
      title: '今日活跃次数',
      value: '12',
      change: '+8%',
      changeColor: '#E67E22',
      changeIcon: 'ant-design:arrow-up-outlined',
      background: 'linear-gradient(135deg, #E74C3C 0%, #C0392B 100%)',
      icon: 'ant-design:fire-outlined',
    },
    {
      title: '今日作业任务',
      value: '47',
      change: '+8%',
      changeColor: '#3498DB',
      changeIcon: 'ant-design:arrow-up-outlined',
      background: 'linear-gradient(135deg, #26B99A 0%, #1ABC9C 100%)',
      icon: 'ant-design:tool-outlined',
    },
    {
      title: 'AR眼镜使用次数',
      value: '32',
      change: '+5%',
      changeColor: '#5DADE2',
      changeIcon: 'ant-design:arrow-up-outlined',
      background: 'linear-gradient(135deg, #3498DB 0%, #2980B9 100%)',
      icon: 'ant-design:eye-outlined',
    },
    {
      title: '工程车辆检修',
      value: '8',
      change: '30%',
      changeColor: '#95A5A6',
      changeIcon: 'ant-design:percentage-outlined',
      background: 'linear-gradient(135deg, #8E44AD 0%, #7D3C98 100%)',
      icon: 'ant-design:car-outlined',
    },
  ]);

  // 城市数据 - 仅展示合肥位置
  const cityData = [
    {
      name: '合肥',
      coord: [117.283, 31.861],
      value: 100,
      status: 'online',
      info: '合肥数据中心<br/>状态：正常运行<br/>节点数：156',
    },
  ];

  // 初始化3D地图
  function initMap() {
    // 使用3D地图配置 - 严格按照 map.html 配置
    const map3DOption = {
      backgroundColor: 'transparent',
      globe: {
        show: false,
      },
      geo3D: {
        map: 'china',
        roam: true,
        itemStyle: {
          color: '#5175e1',
          opacity: 0.8,
          borderWidth: 2,
          borderColor: '#ffffff',
        },
        emphasis: {
          itemStyle: {
            color: '#2a5298',
            borderColor: '#80ff80',
            borderWidth: 3,
          },
          label: {
            show: true,
            color: '#ffffff',
            fontSize: 14,
            fontWeight: 'bold',
            textShadow: '0 0 10px rgba(255, 255, 255, 0.8)',
          },
        },
        light: {
          main: {
            color: '#ffffff',
            intensity: 1.2,
            shadow: true,
            shadowQuality: 'high',
            alpha: 30,
            beta: 40,
          },
          ambient: {
            color: '#40e0ff',
            intensity: 0.4,
          },
        },
        viewControl: {
          autoRotate: false,
          distance: 60,
          alpha: 60,
          beta: 10,
          center: [5, 20, 0],
          animation: true,
          animationDurationUpdate: 1000,
          damping: 0.8,
          rotateSensitivity: 1,
          zoomSensitivity: 1,
          panSensitivity: 1,
        },
        regionHeight: 8,
        postEffect: {
          enable: true,
          bloom: {
            enable: true,
            intensity: 0.3,
          },
          SSAO: {
            enable: true,
            radius: 2,
          },
        },
      },
      series: [
        // 3D地图
        {
          type: 'map3D',
          map: 'china',
          regionHeight: 8,
          itemStyle: {
            color: function (_params) {
              return new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#2563eb' },
                { offset: 1, color: '#1e40af' },
              ]);
            },
            opacity: 0.85,
            borderWidth: 2,
            borderColor: '#40e0ff',
            borderType: 'solid',
          },
          emphasis: {
            itemStyle: {
              color: '#3b82f6',
              borderColor: '#80ff80',
              borderWidth: 3,
            },
            label: {
              show: true,
              color: '#ffffff',
              fontSize: 14,
              fontWeight: 'bold',
              textShadow: '0 0 10px rgba(255, 255, 255, 0.8)',
            },
          },
          light: {
            main: {
              color: '#ffffff',
              intensity: 1.5,
              shadow: true,
              shadowQuality: 'high',
              alpha: 25,
              beta: 45,
            },
            ambient: {
              color: '#40e0ff',
              intensity: 0.5,
            },
          },
        },
        // 城市标注点
        {
          type: 'scatter3D',
          coordinateSystem: 'geo3D',
          data: cityData.map((city) => ({
            name: city.name,
            value: [...city.coord],
            itemStyle: {
              color: '#40e0ff',
              opacity: 0.9,
            },
            status: city.status,
            info: city.info,
          })),
          symbol: 'circle',
          symbolSize: 15,
          itemStyle: {
            borderWidth: 1,
            borderColor: '#ffffff',
          },
          label: {
            show: true,
            position: 'top',
            color: '#ffffff',
            fontSize: 13,
            fontWeight: 'bold',
            backgroundColor: 'rgba(0,0,0,1)',
            padding: [6, 10],
            borderRadius: 6,
            textShadow: '0 0 8px rgba(0, 0, 0, 0.8)',
            formatter: function (params) {
              // 使用 rich text 格式化，添加 SVG 图标和城市名称
              return `{icon|} {name|${params.name}}`;
            },
            rich: {
              icon: {
                backgroundColor: {
                  image:
                    'data:image/svg+xml;base64,' +
                    btoa(`
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M0 6.03437C0 2.70168 2.70168 0 6.03438 0H17.9656C21.2983 0 24 2.70168 24 6.03438V17.9656C24 21.2983 21.2983 24 17.9656 24H6.03437C2.70168 24 0 21.2983 0 17.9656V6.03437Z" fill="#23BBBB"/>
                      <path d="M7.64584 6.86955C10.0255 4.44322 13.8843 4.44322 16.264 6.86955C18.6435 9.29577 18.6433 13.2294 16.264 15.6557L11.9554 20.0492L7.64584 15.6557C5.26659 13.2294 5.2664 9.29576 7.64584 6.86955ZM11.9554 8.80022C10.6612 8.80022 9.61191 9.84883 9.61166 11.143C9.61166 12.4374 10.661 13.4867 11.9554 13.4867C13.2496 13.4865 14.2982 12.4372 14.2982 11.143C14.2979 9.84898 13.2494 8.80046 11.9554 8.80022Z" fill="white"/>
                    </svg>
                  `),
                },
                width: 16,
                height: 16,
                align: 'center',
              },
              name: {
                color: '#ffffff',
                fontSize: 13,
                fontWeight: 'bold',
                textShadow: '0 0 8px rgba(0, 0, 0, 0.8)',
                padding: [0, 0, 0, 4],
              },
            },
          },
          emphasis: {
            itemStyle: {
              opacity: 1,
              borderWidth: 3,
            },
            label: {
              show: true,
              fontSize: 15,
              color: '#ffffff',
              backgroundColor: 'rgba(0,0,0,0.8)',
              padding: [8, 12],
              borderRadius: 8,
              textShadow: '0 0 12px rgba(255, 255, 255, 0.6)',
              formatter: function (params) {
                // 悬停时使用相同的 SVG 图标，但稍大一些
                return `{icon|} {name|${params.name}}`;
              },
              rich: {
                icon: {
                  backgroundColor: {
                    image:
                      'data:image/svg+xml;base64,' +
                      btoa(`
                      <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M0 6.03437C0 2.70168 2.70168 0 6.03438 0H17.9656C21.2983 0 24 2.70168 24 6.03438V17.9656C24 21.2983 21.2983 24 17.9656 24H6.03437C2.70168 24 0 21.2983 0 17.9656V6.03437Z" fill="#23BBBB"/>
                        <path d="M7.64584 6.86955C10.0255 4.44322 13.8843 4.44322 16.264 6.86955C18.6435 9.29577 18.6433 13.2294 16.264 15.6557L11.9554 20.0492L7.64584 15.6557C5.26659 13.2294 5.2664 9.29576 7.64584 6.86955ZM11.9554 8.80022C10.6612 8.80022 9.61191 9.84883 9.61166 11.143C9.61166 12.4374 10.661 13.4867 11.9554 13.4867C13.2496 13.4865 14.2982 12.4372 14.2982 11.143C14.2979 9.84898 13.2494 8.80046 11.9554 8.80022Z" fill="white"/>
                      </svg>
                    `),
                  },
                  width: 18,
                  height: 18,
                  align: 'center',
                },
                name: {
                  color: '#ffffff',
                  fontSize: 15,
                  fontWeight: 'bold',
                  textShadow: '0 0 12px rgba(255, 255, 255, 0.6)',
                  padding: [0, 0, 0, 4],
                },
              },
            },
          },
        },
      ],
      animation: true,
      animationDuration: 2000,
      animationEasing: 'cubicOut',
    };

    try {
      setMapOptions(map3DOption);
    } catch (error) {
      // 如果3D失败，显示错误信息
      console.error('3D地图初始化失败:', error);
    }

    // 添加事件监听
    setTimeout(() => {
      const chartInstance = getInstance();
      if (chartInstance) {
        chartInstance.on('click', handleMapClick);
        chartInstance.on('mouseover', handleMapMouseOver);
        chartInstance.on('mouseout', handleMapMouseOut);
      }
    }, 200);

    // 延迟启动入场动画，确保地图已经渲染完成
    setTimeout(() => {
      startAnimation();
    }, 300);
  }

  function startAnimation() {
    const chartInstance = getInstance();
    if (!chartInstance) return;

    // 初始隐藏地图
    chartInstance.setOption({
      geo3D: {
        regionHeight: 0,
      },
    });

    // 动画显示地图
    setTimeout(() => {
      chartInstance.setOption({
        geo3D: {
          regionHeight: 8,
        },
        series: [
          {
            animationDuration: 2000,
            animationEasing: 'elasticOut',
          },
        ],
      });
    }, 500);

    // 延迟显示城市标注
    setTimeout(() => {
      chartInstance.setOption({
        series: [
          {},
          {
            animationDuration: 1500,
            animationDelay: function (idx) {
              return idx * 200;
            },
          },
        ],
      });
    }, 1500);
  }

  // 处理地图鼠标悬停事件
  function handleMapMouseOver(params) {
    if (params.seriesType === 'scatter' || params.seriesType === 'scatter3D') {
      const cityInfo = cityData.find((city) => city.name === params.name);
      if (cityInfo) {
        showCityTooltip(params.event.offsetX, params.event.offsetY, cityInfo);
      }
    }
  }

  // 处理地图鼠标离开事件
  function handleMapMouseOut() {
    hideCityTooltip();
  }

  // 显示城市信息提示框
  function showCityTooltip(x, y, cityInfo) {
    let tooltip = document.querySelector('.city-tooltip');
    if (!tooltip) {
      tooltip = document.createElement('div');
      tooltip.className = 'city-tooltip';
      document.body.appendChild(tooltip);
    }

    tooltip.innerHTML = `
      <div style="font-weight: bold; color: ${cityInfo.status === 'online' ? '#40e0ff' : '#ff6b35'}; margin-bottom: 8px;">
        ${cityInfo.name}
      </div>
      <div style="line-height: 1.5;">
        ${cityInfo.info}
      </div>
    `;

    tooltip.style.left = x + 15 + 'px';
    tooltip.style.top = y - 10 + 'px';
    tooltip.style.display = 'block';
  }

  // 隐藏提示框
  function hideCityTooltip() {
    const tooltip = document.querySelector('.city-tooltip');
    if (tooltip) {
      tooltip.style.display = 'none';
    }
  }

  // 加载中国地图数据
  async function loadChinaMapData() {
    try {
      // 直接从 public 目录加载地图数据
      const response = await fetch('/json/100000_full.json');

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const geoJson = await response.json();

      // 验证地图数据格式
      if (!geoJson || !geoJson.features) {
        throw new Error('Invalid map data format');
      }

      // 注册地图数据到 ECharts
      echarts.registerMap('china', geoJson);

      // 等待一帧后初始化地图，确保注册完成
      requestAnimationFrame(() => {
        initMap();
      });
    } catch (error) {
      // 如果加载失败，显示错误信息
      console.error('地图数据加载失败:', error);
    }
  }

  // 处理地图点击事件
  function handleMapClick(params) {
    if (params.seriesType === 'scatter' || params.seriesType === 'scatter3D') {
      const cityInfo = cityData.find((city) => city.name === params.name);
      if (cityInfo) {
        // 这里可以添加弹窗显示详细信息的逻辑
        // 例如：showLocationDetail(cityInfo);
      }
    }
  }

  onMounted(() => {
    setTimeout(() => {
      loadChinaMapData();
    }, 100);
  });
</script>

<style scoped>
  /* 响应式设计 */
  @media (max-width: 1400px) {
    .metrics-grid {
      grid-template-columns: repeat(2, 1fr);
      grid-template-rows: repeat(2, 1fr);
      gap: 12px;
    }

    .metrics-container {
      height: 180px;
    }

    .metric-content {
      padding: 15px;
    }

    .metric-value {
      font-size: 28px;
    }
  }

  @media (max-width: 1200px) {
    .map-chart {
      min-height: 250px;
    }

    .metrics-grid {
      grid-template-columns: repeat(4, 1fr);
      grid-template-rows: 1fr;
    }

    .metrics-container {
      height: 120px;
    }

    .metric-content {
      padding: 12px;
    }

    .metric-title {
      font-size: 12px;
    }

    .metric-value {
      font-size: 24px;
    }
  }

  @media (max-width: 768px) {
    .metrics-grid {
      grid-template-columns: repeat(2, 1fr);
      grid-template-rows: repeat(2, 1fr);
    }

    .metrics-container {
      height: 160px;
    }
  }

  .center-panel {
    display: flex;
    flex-direction: column;
    gap: 20px;
    height: 100%;
  }

  .map-container {
    flex: 1;
    overflow: hidden;
  }

  .map-header {
    padding: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 10%);
  }

  .map-title {
    margin: 0;
    color: #fff;
    font-size: 18px;
    font-weight: 600;
    text-align: center;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 50%);
  }

  .map-chart {
    height: 100%;
    min-height: 300px;
  }

  .metrics-container {
    flex-shrink: 0;
    height: 140px;
  }

  .metrics-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 15px;
    height: 100%;
  }

  .metric-card {
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 10%);
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 30%);
    cursor: pointer;
    backdrop-filter: blur(10px);
  }

  .metric-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 40%);
  }

  .metric-content {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100%;
    padding: 20px;
  }

  .metric-header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    margin-bottom: 10px;
  }

  .metric-title {
    color: rgba(255, 255, 255, 90%);
    font-size: 14px;
    font-weight: 500;
    line-height: 1.2;
  }

  .metric-icon {
    color: rgba(255, 255, 255, 80%);
  }

  .metric-value {
    margin: 8px 0;
    color: #fff;
    font-size: 32px;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 50%);
  }

  .metric-change {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    font-weight: 500;
  }

  /* 城市提示框样式 - 基于 map.html 中的配置 */
  :global(.city-tooltip) {
    display: none;
    position: absolute;
    z-index: 1000;
    padding: 12px;
    border: 1px solid #40e0ff;
    border-radius: 8px;
    background: rgba(0, 20, 40, 95%);
    box-shadow: 0 8px 32px rgba(64, 224, 255, 30%);
    color: #fff;
    font-size: 14px;
    pointer-events: none;
    backdrop-filter: blur(10px);
  }
</style>
